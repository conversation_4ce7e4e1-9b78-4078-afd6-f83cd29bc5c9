<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Metadata Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 8px; }
        .video-link { 
            display: inline-block; 
            margin: 10px 0; 
            padding: 10px 15px; 
            background: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
        }
        .video-link:hover { background: #0056b3; }
        .comparison { display: flex; gap: 20px; }
        .comparison > div { flex: 1; }
    </style>
</head>
<body>
    <h1>Video Metadata Debug Test</h1>
    <p>Testing video metadata display for the same video using different API sources</p>
    
    <div class="comparison">
        <div class="test-section">
            <h2>YouTube Data API v3</h2>
            <p>Video fetched using YouTube Data API v3</p>
            <a href="/watch?v=youtube-bnVUHWCynig" class="video-link" target="_blank">
                Test youtube-bnVUHWCynig
            </a>
            <p><strong>Expected:</strong> Should show correct metadata (channel name, icon, view count)</p>
        </div>
        
        <div class="test-section">
            <h2>Google Custom Search JSON API</h2>
            <p>Video fetched using Google Custom Search JSON API</p>
            <a href="/watch?v=google-search-bnVUHWCynig" class="video-link" target="_blank">
                Test google-search-bnVUHWCynig
            </a>
            <p><strong>Expected:</strong> Should show correct metadata (channel name, icon, view count)</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Raw Video ID</h2>
        <p>Video with clean ID (no prefix)</p>
        <a href="/watch?v=bnVUHWCynig" class="video-link" target="_blank">
            Test bnVUHWCynig
        </a>
        <p><strong>Expected:</strong> Should show correct metadata</p>
    </div>
    
    <div class="test-section">
        <h2>Debug Information</h2>
        <p>Open browser console to see detailed logging for video metadata fetching</p>
        <ul>
            <li>Check network requests to see which APIs are being called</li>
            <li>Check console logs for video metadata details</li>
            <li>Compare the data structures returned by each API</li>
        </ul>
    </div>
    
    <script>
        console.log('Video Metadata Debug Test Page Loaded');
        console.log('Test video ID: bnVUHWCynig');
        console.log('YouTube URL: https://www.youtube.com/watch?v=bnVUHWCynig');
    </script>
</body>
</html>
