// Quick test to verify Google Custom Search API for recommendations
// This script tests the recommendation functionality

const API_KEY = 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ';
const ENGINE_ID = '61201925358ea4e83';

async function testGoogleCustomSearchAPI() {
  const testQuery = 'Adele Rolling in the Deep site:youtube.com';
  const searchUrl = new URL('https://www.googleapis.com/customsearch/v1');
  searchUrl.searchParams.set('key', API_KEY);
  searchUrl.searchParams.set('cx', ENGINE_ID);
  searchUrl.searchParams.set('q', testQuery);
  searchUrl.searchParams.set('num', '5');

  console.log('🔍 Testing Google Custom Search API...');
  console.log('Query:', testQuery);
  console.log('URL:', searchUrl.toString());

  try {
    const response = await fetch(searchUrl.toString());
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }

    const data = await response.json();
    
    if (data.error) {
      console.error('❌ API Error:', data.error);
      return;
    }

    console.log('✅ API Response Success!');
    console.log('Total Results:', data.searchInformation?.totalResults || 0);
    console.log('Items Found:', data.items?.length || 0);

    if (data.items && data.items.length > 0) {
      console.log('\n📺 Sample Results:');
      data.items.slice(0, 3).forEach((item, index) => {
        console.log(`${index + 1}. ${item.title}`);
        console.log(`   URL: ${item.link}`);
        console.log(`   Snippet: ${item.snippet.substring(0, 100)}...`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Network Error:', error);
  }
}

// Run the test
testGoogleCustomSearchAPI();
